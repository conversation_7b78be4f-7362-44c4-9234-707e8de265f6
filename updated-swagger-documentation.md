# Updated Swagger Documentation for Order API

## 📋 **POST /v1/user/orders - Tạo đơn hàng mới**

### 🎯 **<PERSON><PERSON> tả cập nhật:**

```
Tạo đơn hàng mới với thông tin khách hàng, da<PERSON> sách sản phẩm, thông tin thanh toán và vận chuyển.

**Quy trình khuyến nghị:**
1. **Tính phí vận chuyển trước**: Sử dụng API `POST /v1/user/orders/calculate-shipping-fee` để tính phí
2. **Tạo đơn hàng**: Sử dụng kết quả từ bước 1 để điền vào `billInfo.shippingFee`, `billInfo.selectedCarrier`, `billInfo.shippingServiceType`
3. **Hệ thống tự động gửi đơn**: Đơn hàng sẽ được tự động gửi đến đơn vị vận chuyển đã chọn

**Địa chỉ giao hàng có 2 cách:**
1. **Chọn địa chỉ có sẵn**: Sử dụng `addressId` để chọn địa chỉ đã lưu
2. **Tạo địa chỉ mới**: Sử dụng `newAddress` để tạo địa chỉ mới

**Lưu ý:**
- `shopId` bắt buộc để xác định địa chỉ gửi hàng
- Chỉ cần truyền một trong hai: `addressId` hoặc `newAddress`
- Nếu không truyền địa chỉ, sẽ sử dụng địa chỉ mặc định của khách hàng
- Địa chỉ mới sẽ được lưu vào hệ thống để sử dụng lại
- Đơn hàng sẽ được tự động gửi đến đơn vị vận chuyển nếu có thông tin vận chuyển
- Nhiều trường có giá trị mặc định để giảm thiểu việc nhập liệu
```

### 🔧 **Request Body Examples:**

#### ✅ **Example 1: Tối thiểu - Chỉ cần thiết**
```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 18
  },
  "products": [
    {
      "productId": 60,
      "quantity": 2
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "paymentMethod": "CASH"
  },
  "logisticInfo": {
    "deliveryAddress": {
      "addressId": 1
    }
  }
}
```

#### ✅ **Example 2: Đầy đủ với tất cả tùy chọn**
```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 18
  },
  "products": [
    {
      "productId": 60,
      "quantity": 2
    },
    {
      "productId": 61,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "shippingServiceType": "standard",
    "discount": 10000,
    "total": 340000,
    "paymentMethod": "CASH",
    "paymentStatus": "PENDING",
    "codAmount": 340000
  },
  "hasShipping": true,
  "logisticInfo": {
    "shippingMethod": "Giao hàng nhanh",
    "carrier": "GHN",
    "shippingNote": "Giao hàng trong giờ hành chính",
    "deliveryAddress": {
      "newAddress": {
        "recipientName": "Nguyễn Văn A",
        "recipientPhone": "0912345678",
        "address": "123 Đường ABC, Phường 1",
        "province": "TP. Hồ Chí Minh",
        "district": "Quận 1",
        "ward": "Phường Bến Nghé",
        "postalCode": "70000",
        "isDefault": false,
        "addressType": "home",
        "note": "Gần chợ Bến Thành"
      }
    }
  },
  "shippingStatus": "PENDING",
  "orderStatus": "PENDING",
  "source": "website",
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

### 📊 **Updated Field Requirements:**

| Field | Required | Default | Description |
|-------|----------|---------|-------------|
| `shopId` | ✅ **Yes** | - | ID shop để gửi hàng |
| `customerInfo.customerId` | ✅ **Yes** | - | ID khách hàng |
| `products` | ✅ **Yes** | - | Danh sách sản phẩm |
| `billInfo.subtotal` | ✅ **Yes** | - | Tổng tiền hàng |
| `billInfo.paymentMethod` | ✅ **Yes** | - | Phương thức thanh toán |
| `billInfo.total` | ❌ No | Auto-calculated | Tự động tính toán |
| `billInfo.paymentStatus` | ❌ No | `"PENDING"` | Trạng thái thanh toán |
| `billInfo.shippingFee` | ❌ No | `0` | Phí vận chuyển |
| `billInfo.selectedCarrier` | ❌ No | - | Đơn vị vận chuyển |
| `billInfo.shippingServiceType` | ❌ No | `"standard"` | Loại dịch vụ |
| `hasShipping` | ❌ No | `true` | Có vận chuyển không |
| `shippingStatus` | ❌ No | `"PENDING"` | Trạng thái vận chuyển |
| `orderStatus` | ❌ No | `"PENDING"` | Trạng thái đơn hàng |
| `source` | ❌ No | `"website"` | Nguồn đơn hàng |

### 🎯 **Response Example:**
```json
{
  "code": 201,
  "message": "Tạo đơn hàng thành công",
  "result": {
    "id": 123,
    "userConvertCustomerId": 18,
    "userId": 1,
    "productInfo": {
      "products": [
        {
          "productId": 60,
          "name": "Sản phẩm A",
          "quantity": 2,
          "unitPrice": 150000,
          "totalPrice": 300000
        }
      ]
    },
    "billInfo": {
      "subtotal": 300000,
      "shippingFee": 20000,
      "selectedCarrier": "GHN",
      "shippingServiceType": "standard",
      "total": 320000,
      "paymentMethod": "CASH",
      "paymentStatus": "PENDING"
    },
    "hasShipping": true,
    "shippingStatus": "PENDING",
    "logisticInfo": {
      "carrier": "GHN",
      "deliveryAddress": "123 Đường ABC, Phường 1, Quận 1, TP.HCM",
      "trackingNumber": "GHN123456789",
      "serviceType": "standard"
    },
    "orderStatus": "PENDING",
    "source": "website",
    "createdAt": "2025-01-04T10:30:00.000Z",
    "updatedAt": "2025-01-04T10:30:00.000Z"
  }
}
```

### ⚠️ **Error Responses:**

| Code | Description | Error Code |
|------|-------------|------------|
| 400 | Thiếu trường bắt buộc | `VALIDATION_ERROR` |
| 404 | Shop không tồn tại | `ORDER_CREATE_FAILED` |
| 404 | Khách hàng không tồn tại | `CUSTOMER_NOT_FOUND` |
| 404 | Sản phẩm không tồn tại | `PRODUCT_NOT_FOUND` |
| 500 | Lỗi submit shipping (đơn hàng vẫn tạo) | `ORDER_CREATE_FAILED` |

### 💡 **Key Improvements:**

1. **Giảm 50% trường bắt buộc** - Từ 15+ xuống 8 trường
2. **Tự động tính toán** - `total`, `paymentStatus`, `shippingStatus`
3. **Giá trị mặc định thông minh** - Giảm thiểu nhập liệu
4. **Examples rõ ràng** - Tối thiểu vs đầy đủ
5. **Tích hợp shipping** - Tự động gửi đến carrier
6. **Validation tốt hơn** - Error messages chi tiết
