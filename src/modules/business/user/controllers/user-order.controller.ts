import { Body, Controller, Get, HttpCode, HttpStatus, Logger, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserOrderService } from '../services/user-order.service';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, CreateUserOrderDto } from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@modules/auth/guards/jwt.util';

/**
 * Controller xử lý các request liên quan đến đơn hàng của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_ORDER)
@Controller('user/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, UserOrderResponseDto, UserOrderListItemDto, UserOrderStatusResponseDto, CreateUserOrderDto, PaginatedResult)
export class UserOrderController {
  private readonly logger = new Logger(UserOrderController.name);

  constructor(private readonly userOrderService: UserOrderService) {}

  /**
   * Tạo đơn hàng mới
   * @param createOrderDto DTO chứa thông tin đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin đơn hàng đã tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo đơn hàng mới',
    description: `Tạo đơn hàng mới với thông tin khách hàng, danh sách sản phẩm, thông tin thanh toán và vận chuyển.

**Quy trình khuyến nghị:**
1. **Tính phí vận chuyển trước**: Sử dụng API \`POST /v1/user/orders/calculate-shipping-fee\` để tính phí
2. **Tạo đơn hàng**: Sử dụng kết quả từ bước 1 để điền vào \`billInfo.shippingFee\`, \`billInfo.selectedCarrier\`, \`billInfo.shippingServiceType\`
3. **Hệ thống tự động gửi đơn**: Đơn hàng sẽ được tự động gửi đến đơn vị vận chuyển đã chọn

**Địa chỉ giao hàng có 2 cách:**
1. **Chọn địa chỉ có sẵn**: Sử dụng \`addressId\` để chọn địa chỉ đã lưu
2. **Tạo địa chỉ mới**: Sử dụng \`newAddress\` để tạo địa chỉ mới

**Lưu ý:**
- \`shopId\` bắt buộc để xác định địa chỉ gửi hàng
- Chỉ cần truyền một trong hai: \`addressId\` hoặc \`newAddress\`
- Nếu không truyền địa chỉ, sẽ sử dụng địa chỉ mặc định của khách hàng
- Địa chỉ mới sẽ được lưu vào hệ thống để sử dụng lại
- Đơn hàng sẽ được tự động gửi đến đơn vị vận chuyển nếu có thông tin vận chuyển`
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo đơn hàng thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            result: { $ref: '#/components/schemas/UserOrderResponseDto' }
          }
        }
      ],
      example: {
        code: 201,
        message: "Tạo đơn hàng thành công",
        result: {
          id: 123,
          userConvertCustomerId: 18,
          userId: 1,
          productInfo: {
            products: [
              {
                productId: 60,
                name: "Sản phẩm A",
                quantity: 2,
                unitPrice: 150000,
                totalPrice: 300000
              }
            ]
          },
          billInfo: {
            subtotal: 300000,
            shippingFee: 20000,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            total: 320000,
            paymentMethod: "CASH",
            paymentStatus: "PENDING"
          },
          hasShipping: true,
          shippingStatus: "PENDING",
          logisticInfo: {
            carrier: "GHN",
            deliveryAddress: "123 Đường ABC, Phường 1, Quận 1, TP.HCM",
            trackingNumber: "GHN123456789",
            serviceType: "standard"
          },
          orderStatus: "PENDING",
          source: "website",
          createdAt: "2025-01-04T10:30:00.000Z",
          updatedAt: "2025-01-04T10:30:00.000Z"
        }
      }
    }
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
    BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND
  )
  async createOrder(
    @Body() createOrderDto: CreateUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${user.id}`);
      const order = await this.userOrderService.createOrder(user.id, createOrderDto);
      return ApiResponseDto.success(order, 'Tạo đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách đơn hàng với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách đơn hàng',
    description: 'Lấy danh sách đơn hàng của người dùng với thông tin khách hàng chuyển đổi đầy đủ (thay vì chỉ userConvertCustomerId)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách đơn hàng với thông tin khách hàng đầy đủ',
    schema: ApiResponseDto.getPaginatedSchema(UserOrderListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.ORDER_FIND_FAILED)
  async getOrders(
    @Query() queryDto: QueryUserOrderDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserOrderListItemDto>>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${user.id}`);
      const orders = await this.userOrderService.findAll(user.id, queryDto);
      return ApiResponseDto.success(orders, 'Lấy danh sách đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết đơn hàng
   */
  @Get('detail/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết đơn hàng' })
  @ApiParam({ name: 'id', description: 'ID của đơn hàng', type: Number })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết đơn hàng',
    schema: ApiResponseDto.getSchema(UserOrderResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED
  )
  async getOrderDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderResponseDto>> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${user.id}`);
      const order = await this.userOrderService.findById(id, user.id);
      return ApiResponseDto.success(order, 'Lấy chi tiết đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   */
  @Get('status-stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy thống kê trạng thái đơn hàng và vận chuyển',
    description: 'Lấy thống kê số lượng đơn hàng theo trạng thái đơn hàng và trạng thái vận chuyển của người dùng hiện tại',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thống kê thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: { $ref: '#/components/schemas/UserOrderStatusResponseDto' },
          },
        },
      ],
    },
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
  )
  async getOrderStatusStats(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserOrderStatusResponseDto>> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${user.id}`);
      const stats = await this.userOrderService.getOrderStatusStats(user.id);
      return ApiResponseDto.success(stats, 'Lấy thống kê trạng thái đơn hàng thành công');
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      throw error;
    }
  }
}
