/**
 * Enum định nghĩa các phương thức vận chuyển
 */
export enum ShippingMethodEnum {
  /**
   * Giao hàng nhanh
   */
  EXPRESS = 'Giao hàng nhanh',

  /**
   * Giao hàng tiết kiệm
   */
  ECONOMY = 'Giao hàng tiết kiệm',

  /**
   * Giao hàng hỏa tốc
   */
  URGENT = 'Giao hàng hỏa tốc',

  /**
   * Giao hàng trong ngày
   */
  SAME_DAY = 'Giao hàng trong ngày',

  /**
   * Giao hàng tiêu chuẩn
   */
  STANDARD = 'Giao hàng tiêu chuẩn',

  /**
   * Giao hàng đường bộ
   */
  ROAD = 'Giao hàng đường bộ',

  /**
   * Giao hàng đường hàng không
   */
  AIR = 'Giao hàng đường hàng không'
}

/**
 * <PERSON><PERSON> tả phương thức vận chuyển bằng tiếng Việt
 */
export const SHIPPING_METHOD_DESCRIPTIONS = {
  [ShippingMethodEnum.EXPRESS]: 'Giao hàng nhanh - 1-2 ngày',
  [ShippingMethodEnum.ECONOMY]: 'Giao hàng tiết kiệm - 3-5 ngày',
  [ShippingMethodEnum.URGENT]: 'Giao hàng hỏa tốc - trong 24h',
  [ShippingMethodEnum.SAME_DAY]: 'Giao hàng trong ngày - cùng ngày',
  [ShippingMethodEnum.STANDARD]: 'Giao hàng tiêu chuẩn - 2-3 ngày',
  [ShippingMethodEnum.ROAD]: 'Giao hàng đường bộ - 3-7 ngày',
  [ShippingMethodEnum.AIR]: 'Giao hàng đường hàng không - 1-2 ngày'
};

/**
 * Mapping từ carrier đến shipping method mặc định
 */
export const CARRIER_DEFAULT_SHIPPING_METHOD = {
  'GHN': ShippingMethodEnum.EXPRESS,
  'GHTK': ShippingMethodEnum.ECONOMY,
  'AHAMOVE': ShippingMethodEnum.SAME_DAY,
  'JT': ShippingMethodEnum.STANDARD
};
