# Tối Ưu Hóa Request Tạo Đơn Hàng

## 🎯 **<PERSON><PERSON><PERSON>i<PERSON>**: <PERSON><PERSON><PERSON><PERSON> thiểu thông tin người dùng phải nhập

## ✅ **Request Tối Ưu - Chỉ Cần Thiết**

### Trường Hợp 1: <PERSON>ử dụng Địa Chỉ Có Sẵn
```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 18
  },
  "products": [
    {
      "productId": 60,
      "quantity": 2
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "paymentMethod": "CASH"
  },
  "logisticInfo": {
    "deliveryAddress": {
      "addressId": 1
    }
  }
}
```

### Trường Hợp 2: Tạo Địa Chỉ Mới
```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 18
  },
  "products": [
    {
      "productId": 60,
      "quantity": 2
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "paymentMethod": "CASH"
  },
  "logisticInfo": {
    "deliveryAddress": {
      "newAddress": {
        "recipientName": "Nguyễn Văn A",
        "recipientPhone": "0912345678",
        "address": "123 Đường ABC, Phường 1",
        "province": "TP. Hồ Chí Minh",
        "district": "Quận 1",
        "ward": "Phường Bến Nghé"
      }
    }
  }
}
```

### Trường Hợp 3: Đơn Hàng Có Thuế và Giảm Giá
```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 18
  },
  "products": [
    {
      "productId": 60,
      "quantity": 2
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "discount": 10000,
    "paymentMethod": "CASH",
    "codAmount": 340000
  },
  "logisticInfo": {
    "shippingNote": "Giao hàng trong giờ hành chính",
    "deliveryAddress": {
      "addressId": 1
    }
  },
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

## 🔄 **So Sánh: Trước và Sau**

### ❌ **Trước (Request Dài)**
```json
{
  "shopId": 1,
  "customerInfo": { "customerId": 18 },
  "products": [{ "productId": 60, "quantity": 2 }],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "shippingServiceType": "standard",  // ← Có thể bỏ
    "discount": 10000,
    "total": 340000,                    // ← Tự động tính
    "paymentMethod": "CASH",
    "paymentStatus": "PENDING",         // ← Mặc định
    "codAmount": 340000
  },
  "hasShipping": true,                  // ← Mặc định
  "logisticInfo": {
    "shippingMethod": "Giao hàng nhanh", // ← Có thể bỏ
    "carrier": "GHN",                   // ← Trùng với selectedCarrier
    "shippingNote": "Giao hàng trong giờ hành chính",
    "deliveryAddress": {
      "addressId": 1,
      "newAddress": { ... }             // ← Chỉ cần 1 trong 2
    }
  },
  "shippingStatus": "pending",          // ← Mặc định
  "orderStatus": "pending",             // ← Mặc định
  "source": "website",                  // ← Mặc định
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

### ✅ **Sau (Request Ngắn)**
```json
{
  "shopId": 1,
  "customerInfo": { "customerId": 18 },
  "products": [{ "productId": 60, "quantity": 2 }],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "discount": 10000,
    "paymentMethod": "CASH",
    "codAmount": 340000
  },
  "logisticInfo": {
    "shippingNote": "Giao hàng trong giờ hành chính",
    "deliveryAddress": { "addressId": 1 }
  },
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

## 🚀 **Các Trường Được Tự Động Xử Lý**

| Trường | Giá Trị Mặc Định | Ghi Chú |
|--------|------------------|---------|
| `billInfo.total` | Tự động tính | `subtotal + shippingFee + tax - discount` |
| `billInfo.paymentStatus` | `"PENDING"` | Có thể override |
| `billInfo.shippingServiceType` | `"standard"` | Từ selectedCarrier |
| `hasShipping` | `true` | Nếu có logisticInfo |
| `shippingStatus` | `"PENDING"` | Trạng thái ban đầu |
| `orderStatus` | `"PENDING"` | Trạng thái ban đầu |
| `source` | `"website"` | Có thể override |
| `logisticInfo.carrier` | Từ `billInfo.selectedCarrier` | Tự động sync |
| `logisticInfo.shippingMethod` | Từ carrier | GHN → "Giao hàng nhanh" |

## 💡 **Lợi Ích**

1. **Giảm 40-50% số trường cần nhập**
2. **Tránh lỗi nhập liệu** (total tự động tính)
3. **Tránh dữ liệu mâu thuẫn** (carrier tự động sync)
4. **UX tốt hơn** cho frontend
5. **Vẫn đầy đủ tính năng** cho các trường hợp phức tạp

## 🔧 **Cách Sử dụng**

1. **Frontend chỉ cần thu thập**:
   - Shop ID
   - Customer ID  
   - Products + quantities
   - Subtotal (từ cart)
   - Shipping fee (từ calculate API)
   - Selected carrier (từ calculate API)
   - Payment method
   - Delivery address

2. **Backend tự động xử lý**:
   - Tính total
   - Set default statuses
   - Sync carrier info
   - Submit to shipping provider
