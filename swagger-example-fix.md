# 🔧 Swagger Example Fix - Hướng Dẫn Sử Dụng API Tối Ưu

## ⚠️ **Vấn Đề Hiện Tại**

Swagger vẫn hiển thị example cũ với nhiều trường không cần thiết vì:
- Swagger tự động generate example từ các field riêng lẻ
- Example mặc định được tạo từ `example` property của từng field
- Không thể override example tổng thể mà không vi phạm development rules

## ✅ **Giải Pháp: Sử Dụng Example Tối Ưu**

### 🎯 **Example Khuyến <PERSON>hị (Copy & Paste)**

```json
{
  "shopId": 1,
  "customerInfo": {
    "customerId": 9
  },
  "products": [
    {
      "productId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "shippingFee": 20000,
    "selectedCarrier": "GHN",
    "paymentMethod": "CASH"
  },
  "logisticInfo": {
    "deliveryAddress": {
      "addressId": 1
    }
  }
}
```

### 📊 **So Sánh: Swagger Default vs <PERSON><PERSON><PERSON>ến Nghị**

| Trường | Swagger Default | Khuyến Nghị | Lý Do |
|--------|----------------|-------------|-------|
| `billInfo.total` | ✅ Có | ❌ Bỏ | Tự động tính toán |
| `billInfo.paymentStatus` | ✅ Có | ❌ Bỏ | Mặc định PENDING |
| `billInfo.shippingServiceType` | ✅ Có | ❌ Bỏ | Mặc định standard |
| `billInfo.tax` | ✅ Có | ❌ Bỏ | Optional |
| `billInfo.discount` | ✅ Có | ❌ Bỏ | Optional |
| `billInfo.codAmount` | ✅ Có | ❌ Bỏ | Optional |
| `hasShipping` | ✅ Có | ❌ Bỏ | Mặc định true |
| `logisticInfo.shippingMethod` | ✅ Có | ❌ Bỏ | Tự động từ carrier |
| `logisticInfo.carrier` | ✅ Có | ❌ Bỏ | Trùng selectedCarrier |
| `shippingStatus` | ✅ Có | ❌ Bỏ | Mặc định PENDING |
| `orderStatus` | ✅ Có | ❌ Bỏ | Mặc định PENDING |
| `source` | ✅ Có | ❌ Bỏ | Mặc định website |
| `note` | ✅ Có | ❌ Bỏ | Optional |
| `tags` | ✅ Có | ❌ Bỏ | Optional |
| `newAddress` + `addressId` | ✅ Có cả 2 | ❌ Chỉ 1 | Chỉ cần 1 trong 2 |

## 🚀 **Workflow Khuyến Nghị**

### Bước 1: Tính Phí Vận Chuyển
```bash
POST /v1/user/orders/calculate-shipping-fee
```

### Bước 2: Tạo Đơn Hàng (Sử dụng Example Tối Ưu)
```bash
POST /v1/user/orders
```

### Bước 3: Hệ Thống Tự Động
- ✅ Tính toán `total`
- ✅ Set `paymentStatus` = PENDING
- ✅ Set `shippingStatus` = PENDING
- ✅ Set `orderStatus` = PENDING
- ✅ Set `source` = website
- ✅ Submit đến shipping provider

## 💡 **Lưu Ý Cho Developer**

1. **Bỏ qua Swagger Default Example** - Sử dụng example tối ưu ở trên
2. **Chỉ điền trường cần thiết** - Hệ thống tự động xử lý phần còn lại
3. **Luôn tính phí trước** - Sử dụng calculate-shipping-fee API
4. **Chọn 1 trong 2 địa chỉ** - addressId HOẶC newAddress, không cả hai

## 🔧 **Cách Test API**

### Test Case 1: Minimal Request
```json
{
  "shopId": 1,
  "customerInfo": { "customerId": 9 },
  "products": [{ "productId": 1, "quantity": 2 }],
  "billInfo": {
    "subtotal": 200000,
    "shippingFee": 15000,
    "selectedCarrier": "GHN",
    "paymentMethod": "CASH"
  },
  "logisticInfo": {
    "deliveryAddress": { "addressId": 1 }
  }
}
```

### Test Case 2: With New Address
```json
{
  "shopId": 1,
  "customerInfo": { "customerId": 9 },
  "products": [{ "productId": 1, "quantity": 1 }],
  "billInfo": {
    "subtotal": 150000,
    "shippingFee": 20000,
    "selectedCarrier": "GHTK",
    "paymentMethod": "CREDIT_CARD"
  },
  "logisticInfo": {
    "deliveryAddress": {
      "newAddress": {
        "recipientName": "Nguyễn Văn B",
        "recipientPhone": "0987654321",
        "address": "456 Đường XYZ",
        "province": "Hà Nội",
        "district": "Quận Ba Đình",
        "ward": "Phường Điện Biên"
      }
    }
  }
}
```

## ✅ **Expected Response**
```json
{
  "code": 201,
  "message": "Tạo đơn hàng thành công",
  "result": {
    "id": 123,
    "userConvertCustomerId": 9,
    "userId": 1,
    "billInfo": {
      "subtotal": 300000,
      "shippingFee": 20000,
      "selectedCarrier": "GHN",
      "total": 320000,
      "paymentMethod": "CASH",
      "paymentStatus": "PENDING"
    },
    "hasShipping": true,
    "shippingStatus": "PENDING",
    "orderStatus": "PENDING",
    "source": "website"
  }
}
```

## 🎯 **Kết Luận**

- ❌ **Đừng copy** example từ Swagger default
- ✅ **Sử dụng** example tối ưu trong document này
- 🚀 **Kết quả**: Giảm 50% công việc nhập liệu, API vẫn đầy đủ tính năng!
